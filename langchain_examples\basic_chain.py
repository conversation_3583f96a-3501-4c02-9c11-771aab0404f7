"""
LangChain基础示例
用于与AutoGen进行对比
"""

import os
import sys
import time
from typing import List, Dict, Any

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from langchain_openai import ChatOpenAI
from langchain.prompts import ChatPromptTemplate, MessagesPlaceholder
from langchain.schema import HumanMessage, AIMessage
from langchain.memory import ConversationBufferMemory
from langchain.chains import Conversation<PERSON>hain

from config.llm_config import get_langchain_config


class LangChainQASystem:
    """
    LangChain实现的问答系统
    用于与AutoGen版本进行对比
    """
    
    def __init__(self):
        """初始化LangChain组件"""
        config = get_langchain_config()
        
        # 创建LLM
        self.llm = ChatOpenAI(
            model=config["model"],
            openai_api_key=config["openai_api_key"],
            openai_api_base=config["openai_api_base"],
            temperature=config["temperature"],
            max_tokens=config["max_tokens"],
        )
        
        # 创建记忆组件
        self.memory = ConversationBufferMemory(
            memory_key="chat_history",
            return_messages=True
        )
        
        # 创建提示模板
        self.prompt = ChatPromptTemplate.from_messages([
            ("system", "你是一个有用的AI助手。请简洁、友好地回答用户的问题。"),
            MessagesPlaceholder(variable_name="chat_history"),
            ("human", "{input}")
        ])
        
        # 创建对话链
        self.chain = ConversationChain(
            llm=self.llm,
            memory=self.memory,
            prompt=self.prompt,
            verbose=False
        )
        
        self.conversation_history = []
    
    def ask_question(self, question: str) -> str:
        """
        提问并获取回答
        
        Args:
            question: 用户问题
            
        Returns:
            str: AI回答
        """
        start_time = time.time()
        
        # 调用链
        response = self.chain.predict(input=question)
        
        end_time = time.time()
        
        # 记录对话历史
        self.conversation_history.append({
            "type": "human",
            "content": question,
            "timestamp": start_time
        })
        self.conversation_history.append({
            "type": "ai", 
            "content": response,
            "timestamp": end_time,
            "response_time": end_time - start_time
        })
        
        return response
    
    def get_conversation_history(self) -> List[Dict[str, Any]]:
        """获取对话历史"""
        return self.conversation_history
    
    def get_metrics(self) -> Dict[str, Any]:
        """获取性能指标"""
        if not self.conversation_history:
            return {}
        
        response_times = [
            msg["response_time"] 
            for msg in self.conversation_history 
            if msg["type"] == "ai" and "response_time" in msg
        ]
        
        return {
            "total_messages": len(self.conversation_history),
            "total_exchanges": len(self.conversation_history) // 2,
            "avg_response_time": sum(response_times) / len(response_times) if response_times else 0,
            "total_time": self.conversation_history[-1]["timestamp"] - self.conversation_history[0]["timestamp"] if len(self.conversation_history) > 1 else 0
        }


class SimpleLangChainChat:
    """
    简化的LangChain聊天实现
    用于基础对比
    """
    
    def __init__(self):
        config = get_langchain_config()
        self.llm = ChatOpenAI(
            model=config["model"],
            openai_api_key=config["openai_api_key"],
            openai_api_base=config["openai_api_base"],
            temperature=config["temperature"],
        )
        self.messages = []
    
    def chat(self, user_message: str) -> str:
        """简单的聊天方法"""
        # 添加用户消息
        self.messages.append(HumanMessage(content=user_message))
        
        # 获取AI回复
        response = self.llm(self.messages)
        
        # 添加AI回复到历史
        self.messages.append(response)
        
        return response.content
    
    def get_message_count(self) -> int:
        """获取消息数量"""
        return len(self.messages)


def demo_langchain_basic():
    """演示LangChain基础用法"""
    print("🔗 LangChain基础演示")
    
    # 简单聊天
    chat = SimpleLangChainChat()
    
    questions = [
        "你好！请介绍一下LangChain框架。",
        "LangChain和AutoGen有什么区别？"
    ]
    
    for i, question in enumerate(questions, 1):
        print(f"\n问题 {i}: {question}")
        response = chat.chat(question)
        print(f"回答 {i}: {response}")
    
    print(f"\n总消息数: {chat.get_message_count()}")
    return chat


def demo_langchain_conversation():
    """演示LangChain对话系统"""
    print("💬 LangChain对话系统演示")
    
    qa_system = LangChainQASystem()
    
    questions = [
        "什么是人工智能？",
        "请详细解释一下机器学习。"
    ]
    
    for question in questions:
        print(f"\n用户: {question}")
        response = qa_system.ask_question(question)
        print(f"助手: {response}")
    
    # 显示指标
    metrics = qa_system.get_metrics()
    print(f"\n📊 性能指标:")
    for key, value in metrics.items():
        print(f"  {key}: {value}")
    
    return qa_system


if __name__ == "__main__":
    print("LangChain示例运行")
    
    try:
        # 基础演示
        demo_langchain_basic()
        
        print("\n" + "="*50 + "\n")
        
        # 对话系统演示
        demo_langchain_conversation()
        
    except Exception as e:
        print(f"❌ LangChain演示出错: {e}")
        print("请检查配置和依赖")
