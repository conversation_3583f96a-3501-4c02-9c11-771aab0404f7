[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "autogen-learning-demos"
version = "0.1.0"
description = "AutoGen学习演示项目 - 针对LangChain/LangGraph开发者的快速上手指南"
authors = [
    {name = "AutoGen Learner", email = "<EMAIL>"},
]
dependencies = [
    "autogen-agentchat>=0.2.0",
    "python-dotenv>=1.0.0",
    "openai>=1.0.0",
    "langchain>=0.1.0",
    "langchain-openai>=0.1.0",
    "langgraph>=0.1.0",
    "jupyter>=1.0.0",
    "rich>=13.0.0",
    "asyncio-mqtt>=0.16.0",
]
requires-python = ">=3.9"
readme = "README.md"
license = {text = "MIT"}

[project.optional-dependencies]
dev = [
    "pytest>=7.0.0",
    "black>=23.0.0",
    "isort>=5.0.0",
    "flake8>=6.0.0",
    "mypy>=1.0.0",
]

[tool.black]
line-length = 88
target-version = ['py39']

[tool.isort]
profile = "black"
line_length = 88

[tool.mypy]
python_version = "3.9"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
