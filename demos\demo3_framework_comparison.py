"""
Demo 3: 框架对比实验

学习目标:
1. 实现相同功能的AutoGen和LangChain版本
2. 对比代码复杂度、可读性和性能
3. 理解不同框架的适用场景
4. 形成框架选择的判断标准

对比维度:
- 代码行数和复杂度
- 开发时间和学习曲线
- 功能灵活性和扩展性
- 性能和资源消耗
- 调试和维护难度
"""

import sys
import os
import time
import asyncio
from typing import Dict, Any, List

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from autogen import AssistantAgent, UserProxyAgent
from config.llm_config import get_openai_config, print_config_info
from demos.utils.helpers import (
    print_section_header, 
    print_learning_tip, 
    print_comparison_table,
    measure_execution_time,
    save_conversation_log
)
from langchain_examples.basic_chain import LangChainQASystem, SimpleLangChainChat


class AutoGenQASystem:
    """
    AutoGen实现的问答系统
    用于与LangChain版本进行对比
    """
    
    def __init__(self):
        """初始化AutoGen组件"""
        self.llm_config = get_openai_config()
        
        # 创建助手智能体
        self.assistant = AssistantAgent(
            name="qa_assistant",
            system_message="""你是一个有用的AI助手。
            请简洁、友好地回答用户的问题。
            如果用户说'结束'，请回复'TERMINATE'。""",
            llm_config=self.llm_config,
            human_input_mode="NEVER",
        )
        
        # 创建用户代理
        self.user_proxy = UserProxyAgent(
            name="qa_user",
            human_input_mode="NEVER",
            max_consecutive_auto_reply=1,  # 每次只回复一轮
            is_termination_msg=lambda x: x.get("content", "").rstrip().endswith("TERMINATE"),
            code_execution_config=False,
        )
        
        self.conversation_history = []
        self.metrics = {
            "total_messages": 0,
            "total_exchanges": 0,
            "response_times": [],
            "start_time": None,
            "end_time": None
        }
    
    def ask_question(self, question: str) -> str:
        """
        提问并获取回答
        
        Args:
            question: 用户问题
            
        Returns:
            str: AI回答
        """
        start_time = time.time()
        
        if self.metrics["start_time"] is None:
            self.metrics["start_time"] = start_time
        
        # 启动对话
        result = self.user_proxy.initiate_chat(
            self.assistant,
            message=question,
            silent=True  # 静默模式，减少输出
        )
        
        end_time = time.time()
        self.metrics["end_time"] = end_time
        
        response_time = end_time - start_time
        self.metrics["response_times"].append(response_time)
        self.metrics["total_exchanges"] += 1
        self.metrics["total_messages"] += len(result.chat_history)
        
        # 提取AI回复
        ai_response = ""
        for msg in result.chat_history:
            if msg.get("name") == "qa_assistant":
                ai_response = msg.get("content", "")
                break
        
        # 记录对话历史
        self.conversation_history.extend(result.chat_history)
        
        return ai_response
    
    def get_conversation_history(self) -> List[Dict[str, Any]]:
        """获取对话历史"""
        return self.conversation_history
    
    def get_metrics(self) -> Dict[str, Any]:
        """获取性能指标"""
        response_times = self.metrics["response_times"]
        total_time = 0
        if self.metrics["start_time"] and self.metrics["end_time"]:
            total_time = self.metrics["end_time"] - self.metrics["start_time"]
        
        return {
            "total_messages": self.metrics["total_messages"],
            "total_exchanges": self.metrics["total_exchanges"],
            "avg_response_time": sum(response_times) / len(response_times) if response_times else 0,
            "total_time": total_time
        }


@measure_execution_time
def compare_basic_qa():
    """
    对比基础问答功能
    """
    print_section_header(
        "基础问答功能对比",
        "相同任务的不同实现方式"
    )

    questions = [
        "什么是人工智能？",
        "请解释机器学习的基本概念。"
    ]

    print("🤖 AutoGen版本:")
    autogen_system = AutoGenQASystem()
    autogen_start = time.time()

    for i, question in enumerate(questions, 1):
        print(f"\n问题 {i}: {question}")
        response = autogen_system.ask_question(question)
        print(f"回答 {i}: {response[:100]}...")  # 只显示前100字符

    autogen_end = time.time()
    autogen_metrics = autogen_system.get_metrics()
    autogen_metrics["framework_time"] = autogen_end - autogen_start

    print("\n" + "-"*50)

    print("🔗 LangChain版本:")
    langchain_system = LangChainQASystem()
    langchain_start = time.time()

    for i, question in enumerate(questions, 1):
        print(f"\n问题 {i}: {question}")
        response = langchain_system.ask_question(question)
        print(f"回答 {i}: {response[:100]}...")  # 只显示前100字符

    langchain_end = time.time()
    langchain_metrics = langchain_system.get_metrics()
    langchain_metrics["framework_time"] = langchain_end - langchain_start

    # 对比分析
    print_comparison_table(autogen_metrics, langchain_metrics)

    return autogen_metrics, langchain_metrics


def analyze_code_complexity():
    """
    分析代码复杂度
    """
    print_section_header(
        "代码复杂度分析",
        "对比实现相同功能所需的代码"
    )

    # AutoGen实现的核心代码
    autogen_core_code = '''
# AutoGen核心实现 (约15行)
assistant = AssistantAgent("assistant", llm_config=config)
user_proxy = UserProxyAgent("user", human_input_mode="NEVER")

def ask_question(question):
    result = user_proxy.initiate_chat(assistant, message=question)
    return extract_response(result)
'''

    # LangChain实现的核心代码
    langchain_core_code = '''
# LangChain核心实现 (约20行)
llm = ChatOpenAI(model=model, api_key=key)
memory = ConversationBufferMemory(return_messages=True)
prompt = ChatPromptTemplate.from_messages([...])
chain = ConversationChain(llm=llm, memory=memory, prompt=prompt)

def ask_question(question):
    response = chain.predict(input=question)
    return response
'''

    print("📊 代码复杂度对比:")
    print("\n[AutoGen实现]")
    print(autogen_core_code)

    print("\n[LangChain实现]")
    print(langchain_core_code)

    complexity_comparison = {
        "AutoGen": {
            "核心代码行数": 15,
            "概念数量": 3,  # Agent, UserProxy, initiate_chat
            "配置复杂度": "低",
            "学习曲线": "平缓",
            "可读性": "高"
        },
        "LangChain": {
            "核心代码行数": 20,
            "概念数量": 5,  # LLM, Memory, Prompt, Chain, predict
            "配置复杂度": "中",
            "学习曲线": "中等",
            "可读性": "中"
        }
    }

    print_comparison_table(complexity_comparison["AutoGen"], complexity_comparison["LangChain"])

    return complexity_comparison


def analyze_use_cases():
    """
    分析不同框架的适用场景
    """
    print_section_header(
        "适用场景分析",
        "理解何时选择哪个框架"
    )

    use_cases = {
        "AutoGen优势场景": [
            "🤝 多智能体协作任务",
            "👥 需要人机交互的复杂工作流",
            "🔄 多轮对话和上下文保持",
            "🛠️ 代码生成和执行任务",
            "🏢 企业级应用开发"
        ],
        "LangChain优势场景": [
            "⚡ 单次任务处理",
            "🔗 复杂的数据处理链",
            "📚 RAG和知识库应用",
            "🧩 需要大量预构建组件",
            "🔧 快速原型开发"
        ]
    }

    for framework, scenarios in use_cases.items():
        print(f"\n{framework}:")
        for scenario in scenarios:
            print(f"  {scenario}")

    print_learning_tip(
        "选择框架的关键因素：\n"
        "1. 任务复杂度：简单任务用LangChain，复杂协作用AutoGen\n"
        "2. 交互需求：需要多轮对话选AutoGen\n"
        "3. 团队背景：有对话系统经验选AutoGen\n"
        "4. 生态需求：需要丰富组件选LangChain"
    )


def comprehensive_comparison():
    """
    综合对比分析
    """
    print_section_header(
        "综合对比分析",
        "全方位框架对比总结"
    )

    comprehensive_metrics = {
        "AutoGen": {
            "学习难度": "中等",
            "开发速度": "中等",
            "代码可读性": "高",
            "调试难度": "低",
            "扩展性": "高",
            "社区支持": "中等",
            "文档质量": "好",
            "企业支持": "强",
            "适用场景": "多智能体协作"
        },
        "LangChain": {
            "学习难度": "中等",
            "开发速度": "快",
            "代码可读性": "中",
            "调试难度": "中",
            "扩展性": "中",
            "社区支持": "强",
            "文档质量": "好",
            "企业支持": "中",
            "适用场景": "单任务处理"
        }
    }

    print_comparison_table(comprehensive_metrics["AutoGen"], comprehensive_metrics["LangChain"])

    # 决策建议
    print("\n🎯 框架选择建议:")

    decision_tree = """
    选择AutoGen如果:
    ✅ 需要多智能体协作
    ✅ 需要复杂的对话流程
    ✅ 需要人机交互
    ✅ 有Microsoft生态需求
    ✅ 重视代码可读性

    选择LangChain如果:
    ✅ 单次任务处理
    ✅ 需要快速开发
    ✅ 需要丰富的预构建组件
    ✅ RAG应用开发
    ✅ 有现有LangChain经验
    """

    print(decision_tree)

    return comprehensive_metrics


def main():
    """主函数：运行所有对比实验"""
    try:
        print_config_info()

        print_learning_tip(
            "这个演示将通过实际运行来对比AutoGen和LangChain的差异。"
            "请注意观察代码结构、执行方式和性能指标的不同。"
        )

        # 1. 基础功能对比
        autogen_metrics, langchain_metrics = compare_basic_qa()

        print("\n" + "="*60 + "\n")

        # 2. 代码复杂度分析
        complexity_analysis = analyze_code_complexity()

        print("\n" + "="*60 + "\n")

        # 3. 适用场景分析
        analyze_use_cases()

        print("\n" + "="*60 + "\n")

        # 4. 综合对比
        comprehensive_analysis = comprehensive_comparison()

        # 保存对比结果
        comparison_results = {
            "performance_metrics": {
                "autogen": autogen_metrics,
                "langchain": langchain_metrics
            },
            "complexity_analysis": complexity_analysis,
            "comprehensive_analysis": comprehensive_analysis
        }

        import json
        with open("comparison_results.json", "w", encoding="utf-8") as f:
            json.dump(comparison_results, f, ensure_ascii=False, indent=2, default=str)

        print_section_header(
            "Demo 3 完成！",
            "您已经完成了AutoGen与LangChain的全面对比。"
            "对比结果已保存到 comparison_results.json"
        )

        print_learning_tip(
            "恭喜！您已经完成了AutoGen学习计划的第一阶段。\n"
            "现在您应该能够：\n"
            "1. 理解AutoGen的核心概念和编程范式\n"
            "2. 识别AutoGen与LangChain的关键差异\n"
            "3. 根据需求选择合适的框架\n"
            "4. 快速上手AutoGen开发"
        )

    except Exception as e:
        print(f"❌ 运行出错: {e}")
        print("\n请检查:")
        print("1. 所有依赖是否正确安装")
        print("2. .env文件配置是否正确")
        print("3. 网络连接是否正常")


if __name__ == "__main__":
    main()
