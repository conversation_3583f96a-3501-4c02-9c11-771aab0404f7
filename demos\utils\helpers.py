"""
演示项目的辅助工具函数
"""

import time
import json
from typing import List, Dict, Any
from rich.console import Console
from rich.panel import Panel
from rich.syntax import Syntax
from rich.table import Table

console = Console()


def print_section_header(title: str, description: str = ""):
    """打印章节标题"""
    panel_content = f"[bold blue]{title}[/bold blue]"
    if description:
        panel_content += f"\n[dim]{description}[/dim]"
    
    console.print(Panel(panel_content, expand=False, border_style="blue"))


def print_code_comparison(autogen_code: str, langchain_code: str, title: str = "代码对比"):
    """打印代码对比"""
    console.print(f"\n[bold yellow]{title}[/bold yellow]")
    
    table = Table(show_header=True, header_style="bold magenta")
    table.add_column("AutoGen实现", style="cyan", width=50)
    table.add_column("LangChain实现", style="green", width=50)
    
    # 格式化代码
    autogen_syntax = Syntax(autogen_code, "python", theme="monokai", line_numbers=True)
    langchain_syntax = Syntax(langchain_code, "python", theme="monokai", line_numbers=True)
    
    table.add_row(autogen_syntax, langchain_syntax)
    console.print(table)


def print_message_flow(messages: List[Dict[str, Any]], title: str = "消息流"):
    """打印消息流"""
    console.print(f"\n[bold green]{title}[/bold green]")
    
    for i, msg in enumerate(messages, 1):
        sender = msg.get("name", msg.get("role", "unknown"))
        content = msg.get("content", "")
        
        # 限制内容长度
        if len(content) > 100:
            content = content[:100] + "..."
        
        console.print(f"[bold]{i}. {sender}:[/bold] {content}")


def measure_execution_time(func):
    """装饰器：测量函数执行时间"""
    def wrapper(*args, **kwargs):
        start_time = time.time()
        result = func(*args, **kwargs)
        end_time = time.time()
        execution_time = end_time - start_time
        
        console.print(f"[dim]执行时间: {execution_time:.2f}秒[/dim]")
        return result
    return wrapper


def print_comparison_table(autogen_metrics: Dict[str, Any], langchain_metrics: Dict[str, Any]):
    """打印对比表格"""
    console.print("\n[bold yellow]框架对比分析[/bold yellow]")
    
    table = Table(show_header=True, header_style="bold magenta")
    table.add_column("指标", style="cyan")
    table.add_column("AutoGen", style="green")
    table.add_column("LangChain", style="blue")
    
    # 合并所有指标
    all_metrics = set(autogen_metrics.keys()) | set(langchain_metrics.keys())
    
    for metric in sorted(all_metrics):
        autogen_value = autogen_metrics.get(metric, "N/A")
        langchain_value = langchain_metrics.get(metric, "N/A")
        table.add_row(metric, str(autogen_value), str(langchain_value))
    
    console.print(table)


def save_conversation_log(messages: List[Dict[str, Any]], filename: str):
    """保存对话日志"""
    import os
    log_dir = "logs"
    os.makedirs(log_dir, exist_ok=True)
    
    log_path = os.path.join(log_dir, filename)
    with open(log_path, "w", encoding="utf-8") as f:
        json.dump(messages, f, ensure_ascii=False, indent=2)
    
    console.print(f"[dim]对话日志已保存到: {log_path}[/dim]")


def print_learning_tip(tip: str):
    """打印学习提示"""
    console.print(Panel(
        f"[bold yellow]💡 学习提示[/bold yellow]\n{tip}",
        border_style="yellow",
        expand=False
    ))
