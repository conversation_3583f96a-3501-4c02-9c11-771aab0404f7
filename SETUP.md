# AutoGen学习项目设置指南

## 快速开始

### 1. 环境准备

确保您的系统已安装：
- Python 3.9+
- uv (Python包管理工具)

### 2. 安装uv（如果尚未安装）

**Windows:**
```powershell
powershell -c "irm https://astral.sh/uv/install.ps1 | iex"
```

**macOS/Linux:**
```bash
curl -LsSf https://astral.sh/uv/install.sh | sh
```

### 3. 项目设置

```bash
# 克隆或下载项目到本地
cd autogen-learning-demos

# 创建虚拟环境
uv venv

# 激活虚拟环境
# Windows:
.venv\Scripts\activate
# macOS/Linux:
source .venv/bin/activate

# 安装项目依赖
uv pip install -e .
```

### 4. 配置环境变量

确保 `.env` 文件包含正确的API配置：

```env
OPENAI_API_KEY=sk-zk27b33f9750ab4d8b68c9d2482791ebe9c6670387cf12d2
OPENAI_BASE_URL=https://api.zhizengzeng.com/v1
OPENAI_MODEL=gpt-4o-mini
OPENAI_EMBEDDING_MODEL=text-embedding-3-small
```

### 5. 验证安装

```bash
# 检查配置
python config/llm_config.py

# 运行演示项目
python run_demos.py
```

## 项目结构说明

```
autogen-learning-demos/
├── pyproject.toml              # 项目配置
├── .env                        # 环境变量
├── README.md                   # 项目说明
├── SETUP.md                    # 设置指南
├── run_demos.py               # 统一运行脚本
├── config/
│   └── llm_config.py          # LLM配置管理
├── demos/                     # 演示项目
│   ├── demo1_basic_chat.py    # Demo 1: 基础对话
│   ├── demo2_conversation_control.py  # Demo 2: 对话控制
│   ├── demo3_framework_comparison.py  # Demo 3: 框架对比
│   └── utils/                 # 工具函数
│       ├── __init__.py
│       └── helpers.py
├── langchain_examples/        # LangChain对比示例
│   ├── __init__.py
│   └── basic_chain.py
├── work_dir/                  # AutoGen工作目录
└── logs/                      # 日志文件（运行时创建）
```

## 运行演示

### 方式1：使用统一脚本（推荐）

```bash
python run_demos.py
```

这将显示交互式菜单，您可以选择运行特定演示或所有演示。

### 方式2：单独运行演示

```bash
# Demo 1: AutoGen基础对话
python demos/demo1_basic_chat.py

# Demo 2: 对话控制和终止
python demos/demo2_conversation_control.py

# Demo 3: 框架对比实验
python demos/demo3_framework_comparison.py
```

## 学习路径

### 第一阶段目标
通过这三个演示项目，您将：

1. **理解AutoGen核心概念**
   - ConversableAgent、AssistantAgent、UserProxyAgent
   - 对话机制和消息传递
   - 与LangChain的概念映射

2. **掌握对话控制**
   - 终止条件设置
   - 人机交互模式
   - 自定义控制逻辑

3. **形成框架选择能力**
   - 理解不同框架的优劣势
   - 掌握适用场景判断
   - 建立技术决策标准

### 预期学习成果

完成第一阶段后，您应该能够：
- ✅ 创建和配置AutoGen智能体
- ✅ 实现基本的多智能体对话
- ✅ 理解AutoGen与LangChain的差异
- ✅ 选择合适的框架解决特定问题

## 故障排除

### 常见问题

**1. 导入错误**
```
ModuleNotFoundError: No module named 'autogen'
```
解决方案：确保已激活虚拟环境并安装了依赖
```bash
source .venv/bin/activate  # 或 .venv\Scripts\activate
uv pip install -e .
```

**2. API连接错误**
```
openai.AuthenticationError: Incorrect API key
```
解决方案：检查 `.env` 文件中的API配置

**3. 网络连接问题**
```
requests.exceptions.ConnectionError
```
解决方案：检查网络连接和API基础URL

**4. 权限错误**
```
PermissionError: [Errno 13] Permission denied
```
解决方案：确保有写入工作目录的权限
```bash
mkdir -p work_dir logs
chmod 755 work_dir logs
```

### 调试技巧

1. **启用详细日志**
   ```python
   import logging
   logging.basicConfig(level=logging.DEBUG)
   ```

2. **检查配置**
   ```bash
   python config/llm_config.py
   ```

3. **测试API连接**
   ```python
   from config.llm_config import get_openai_config
   config = get_openai_config()
   print(config)
   ```

## 下一步

完成第一阶段后，您可以：

1. **进入第二阶段**：AutoGen独有特性掌握
   - 代码执行功能
   - 工具集成
   - 群组对话

2. **实践项目**：开发自己的AutoGen应用

3. **深入学习**：阅读AutoGen官方文档和高级示例

## 支持

如果遇到问题：
1. 检查本文档的故障排除部分
2. 查看项目的README.md
3. 参考AutoGen官方文档
4. 检查环境配置和依赖安装

祝您学习愉快！🚀
