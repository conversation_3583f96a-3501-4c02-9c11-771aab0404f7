#!/usr/bin/env python3
"""
AutoGen学习演示项目运行脚本

这个脚本提供了一个统一的入口来运行所有演示项目。
"""

import sys
import os
import subprocess
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.append(str(project_root))

from config.llm_config import print_config_info
from demos.utils.helpers import print_section_header, print_learning_tip


def check_environment():
    """检查环境配置"""
    print_section_header("环境检查", "验证项目配置和依赖")
    
    # 检查.env文件
    env_file = project_root / ".env"
    if not env_file.exists():
        print("❌ .env文件不存在")
        return False
    
    # 检查配置
    try:
        print_config_info()
        print("✅ 配置检查通过")
        return True
    except Exception as e:
        print(f"❌ 配置检查失败: {e}")
        return False


def run_demo(demo_number: int):
    """运行指定的演示"""
    demo_files = {
        1: "demos/demo1_basic_chat.py",
        2: "demos/demo2_conversation_control.py", 
        3: "demos/demo3_framework_comparison.py"
    }
    
    if demo_number not in demo_files:
        print(f"❌ 无效的演示编号: {demo_number}")
        return False
    
    demo_file = demo_files[demo_number]
    demo_path = project_root / demo_file
    
    if not demo_path.exists():
        print(f"❌ 演示文件不存在: {demo_file}")
        return False
    
    print_section_header(f"运行Demo {demo_number}", f"执行 {demo_file}")
    
    try:
        # 运行演示
        result = subprocess.run([
            sys.executable, str(demo_path)
        ], cwd=str(project_root), capture_output=False)
        
        if result.returncode == 0:
            print(f"✅ Demo {demo_number} 运行成功")
            return True
        else:
            print(f"❌ Demo {demo_number} 运行失败，退出码: {result.returncode}")
            return False
            
    except Exception as e:
        print(f"❌ 运行Demo {demo_number}时出错: {e}")
        return False


def run_all_demos():
    """运行所有演示"""
    print_section_header("运行所有演示", "按顺序执行所有学习项目")
    
    success_count = 0
    total_demos = 3
    
    for i in range(1, total_demos + 1):
        print(f"\n{'='*60}")
        if run_demo(i):
            success_count += 1
        print(f"{'='*60}\n")
        
        # 在演示之间暂停
        if i < total_demos:
            input("按Enter键继续下一个演示...")
    
    print_section_header(
        "所有演示完成", 
        f"成功运行 {success_count}/{total_demos} 个演示"
    )
    
    if success_count == total_demos:
        print_learning_tip(
            "🎉 恭喜！您已经完成了AutoGen学习计划的第一阶段！\n\n"
            "您现在应该掌握了：\n"
            "✅ AutoGen的基本概念和使用方法\n"
            "✅ 对话控制和终止机制\n"
            "✅ AutoGen与LangChain的差异对比\n\n"
            "接下来可以进入第二阶段：AutoGen独有特性掌握"
        )


def show_menu():
    """显示菜单"""
    print_section_header("AutoGen学习演示项目", "选择要运行的演示")
    
    menu = """
请选择要运行的演示:

1. Demo 1: AutoGen基础对话
   - 学习AutoGen的核心概念
   - 理解智能体创建和对话机制
   - 对比LangChain的实现方式

2. Demo 2: 对话控制和终止
   - 掌握对话终止条件
   - 理解人机交互模式
   - 学习自定义控制逻辑

3. Demo 3: 框架对比实验
   - 实现相同功能的不同版本
   - 对比性能和复杂度
   - 理解框架选择标准

4. 运行所有演示
   - 按顺序执行所有项目
   - 完整的学习体验

5. 环境检查
   - 验证配置和依赖

0. 退出

请输入选项 (0-5): """
    
    return input(menu).strip()


def main():
    """主函数"""
    print("🚀 欢迎使用AutoGen学习演示项目！")
    
    # 首先检查环境
    if not check_environment():
        print("\n请先配置环境后再运行演示。")
        return
    
    while True:
        try:
            choice = show_menu()
            
            if choice == "0":
                print("👋 感谢使用AutoGen学习演示项目！")
                break
            elif choice == "1":
                run_demo(1)
            elif choice == "2":
                run_demo(2)
            elif choice == "3":
                run_demo(3)
            elif choice == "4":
                run_all_demos()
            elif choice == "5":
                check_environment()
            else:
                print("❌ 无效选项，请重新选择。")
            
            if choice in ["1", "2", "3"]:
                input("\n按Enter键返回主菜单...")
                
        except KeyboardInterrupt:
            print("\n\n👋 用户中断，退出程序。")
            break
        except Exception as e:
            print(f"\n❌ 程序出错: {e}")
            input("按Enter键继续...")


if __name__ == "__main__":
    main()
