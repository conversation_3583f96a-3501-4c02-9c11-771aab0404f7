"""
Demo 1: AutoGen基础对话

学习目标:
1. 理解AutoGen的基本对话机制
2. 掌握AssistantAgent和UserProxyAgent的创建和配置
3. 对比LangChain的Chain调用方式
4. 理解消息传递和对话历史管理

关键概念:
- ConversableAgent: AutoGen中所有智能体的基类
- AssistantAgent: 基于LLM的助手智能体
- UserProxyAgent: 代表用户的代理智能体
- initiate_chat: 启动对话的方法
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from autogen import AssistantAgent, UserProxyAgent
from config.llm_config import get_openai_config, print_config_info
from demos.utils.helpers import (
    print_section_header, 
    print_learning_tip, 
    print_message_flow,
    save_conversation_log
)


def demo_basic_autogen_chat():
    """
    演示AutoGen的基础对话功能
    
    这个函数展示了AutoGen最核心的概念：
    1. 创建智能体
    2. 配置LLM
    3. 启动对话
    4. 消息传递机制
    """
    print_section_header(
        "Demo 1: AutoGen基础对话", 
        "学习AutoGen的核心对话机制和智能体创建"
    )
    
    # 显示配置信息
    print_config_info()
    
    # 获取LLM配置
    llm_config = get_openai_config()
    
    print_learning_tip(
        "AutoGen的核心是'对话'概念。不同于LangChain的链式调用，"
        "AutoGen通过智能体之间的对话来完成任务。"
    )
    
    # 1. 创建AssistantAgent（助手智能体）
    print("\n🤖 创建AssistantAgent...")
    assistant = AssistantAgent(
        name="helpful_assistant",  # 智能体名称，用于标识
        system_message="""你是一个有用的AI助手。
        请用简洁、友好的方式回答用户的问题。
        如果用户说'再见'或'结束'，请回复'TERMINATE'来结束对话。""",
        llm_config=llm_config,  # LLM配置
        human_input_mode="NEVER",  # 永不需要人类输入
    )
    
    # 2. 创建UserProxyAgent（用户代理智能体）
    print("👤 创建UserProxyAgent...")
    user_proxy = UserProxyAgent(
        name="user_proxy",
        human_input_mode="NEVER",  # 自动模式，不需要人类输入
        max_consecutive_auto_reply=3,  # 最多自动回复3次
        is_termination_msg=lambda x: x.get("content", "").rstrip().endswith("TERMINATE"),
        code_execution_config=False,  # 禁用代码执行
        system_message="你代表用户与助手对话。"
    )
    
    print_learning_tip(
        "AssistantAgent负责生成回复，UserProxyAgent代表用户。"
        "这种设计让人机协作变得非常自然。"
    )
    
    # 3. 启动对话
    print("\n💬 启动对话...")
    print("=" * 50)
    
    # 这是AutoGen的核心：通过initiate_chat启动对话
    chat_result = user_proxy.initiate_chat(
        assistant,
        message="你好！请介绍一下AutoGen框架的主要特点。",
        summary_method="reflection_with_llm",  # 使用LLM生成对话摘要
    )
    
    print("=" * 50)
    
    # 4. 分析对话结果
    print("\n📊 对话分析:")
    print(f"对话轮数: {len(chat_result.chat_history)}")
    print(f"对话摘要: {chat_result.summary}")
    
    # 显示消息流
    print_message_flow(chat_result.chat_history, "完整消息流")
    
    # 保存对话日志
    save_conversation_log(chat_result.chat_history, "demo1_basic_chat.json")
    
    return chat_result


def compare_with_langchain():
    """
    对比AutoGen和LangChain的实现方式
    """
    print_section_header(
        "AutoGen vs LangChain 对比", 
        "理解不同框架的编程范式差异"
    )
    
    # AutoGen方式的伪代码
    autogen_approach = """
# AutoGen方式：对话式编程
assistant = AssistantAgent("assistant", llm_config=config)
user_proxy = UserProxyAgent("user", human_input_mode="NEVER")

# 启动对话，智能体自动交互
result = user_proxy.initiate_chat(
    assistant, 
    message="你的问题"
)
"""
    
    # LangChain方式的伪代码
    langchain_approach = """
# LangChain方式：链式编程
from langchain.chains import LLMChain
from langchain.prompts import PromptTemplate

prompt = PromptTemplate(template="回答问题: {question}")
chain = LLMChain(llm=llm, prompt=prompt)

# 直接调用链
result = chain.run(question="你的问题")
"""
    
    print("🔄 编程范式对比:")
    print("\n[AutoGen - 对话式]")
    print(autogen_approach)
    
    print("\n[LangChain - 链式]")
    print(langchain_approach)
    
    print_learning_tip(
        "AutoGen的对话式编程更适合复杂的多轮交互，"
        "而LangChain的链式编程更适合单次任务处理。"
    )


def demonstrate_message_structure():
    """
    演示AutoGen的消息结构
    """
    print_section_header(
        "AutoGen消息结构", 
        "理解AutoGen如何管理对话历史和消息格式"
    )
    
    # 创建简单的智能体进行演示
    llm_config = get_openai_config()
    
    assistant = AssistantAgent(
        name="demo_assistant",
        system_message="你是一个演示助手，请简短回答。",
        llm_config=llm_config,
    )
    
    user_proxy = UserProxyAgent(
        name="demo_user",
        human_input_mode="NEVER",
        max_consecutive_auto_reply=1,
        is_termination_msg=lambda x: True,  # 一轮后就结束
        code_execution_config=False,
    )
    
    # 启动简短对话
    result = user_proxy.initiate_chat(
        assistant,
        message="什么是AI？",
    )
    
    # 分析消息结构
    print("\n📋 消息结构分析:")
    for i, message in enumerate(result.chat_history):
        print(f"\n消息 {i+1}:")
        print(f"  角色 (role): {message.get('role', 'N/A')}")
        print(f"  名称 (name): {message.get('name', 'N/A')}")
        print(f"  内容长度: {len(message.get('content', ''))}")
        print(f"  内容预览: {message.get('content', '')[:50]}...")
    
    print_learning_tip(
        "AutoGen的消息格式遵循OpenAI的标准，包含role、name和content字段。"
        "这使得与其他工具的集成变得容易。"
    )


def main():
    """主函数：运行所有演示"""
    try:
        # 1. 基础对话演示
        demo_basic_autogen_chat()
        
        print("\n" + "="*60 + "\n")
        
        # 2. 框架对比
        compare_with_langchain()
        
        print("\n" + "="*60 + "\n")
        
        # 3. 消息结构演示
        demonstrate_message_structure()
        
        print_section_header(
            "Demo 1 完成！", 
            "您已经学会了AutoGen的基础对话机制。接下来可以运行Demo 2学习对话控制。"
        )
        
    except Exception as e:
        print(f"❌ 运行出错: {e}")
        print("\n请检查:")
        print("1. .env文件中的API配置是否正确")
        print("2. 网络连接是否正常")
        print("3. API密钥是否有效")


if __name__ == "__main__":
    main()
