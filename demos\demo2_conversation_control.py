"""
Demo 2: 对话控制和终止

学习目标:
1. 掌握不同的对话终止条件
2. 理解人机交互模式配置
3. 对比LangGraph的状态管理方式
4. 学习自定义对话控制逻辑

关键概念:
- is_termination_msg: 终止消息判断函数
- human_input_mode: 人机交互模式
- max_consecutive_auto_reply: 最大自动回复次数
- 自定义终止逻辑
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from autogen import AssistantAgent, UserProxyAgent
from config.llm_config import get_openai_config, print_config_info
from demos.utils.helpers import (
    print_section_header, 
    print_learning_tip, 
    print_message_flow,
    save_conversation_log,
    measure_execution_time
)


def demo_termination_conditions():
    """
    演示不同的对话终止条件
    """
    print_section_header(
        "对话终止条件演示", 
        "学习如何控制对话的结束时机"
    )
    
    llm_config = get_openai_config()
    
    print_learning_tip(
        "AutoGen提供了多种终止条件：关键词终止、轮数限制、自定义函数等。"
        "这比LangGraph的图执行完成更加灵活。"
    )
    
    # 1. 关键词终止
    print("\n🔚 演示1: 关键词终止")
    
    assistant1 = AssistantAgent(
        name="assistant_keyword",
        system_message="""你是一个助手。当用户说'结束'时，请回复'TERMINATE'来结束对话。
        否则请简短回答用户的问题。""",
        llm_config=llm_config,
    )
    
    user_proxy1 = UserProxyAgent(
        name="user_keyword",
        human_input_mode="NEVER",
        max_consecutive_auto_reply=5,
        # 关键词终止：检查消息是否包含TERMINATE
        is_termination_msg=lambda x: x.get("content", "").rstrip().endswith("TERMINATE"),
        code_execution_config=False,
    )
    
    result1 = user_proxy1.initiate_chat(
        assistant1,
        message="你好，请告诉我今天是星期几？然后说'结束'。"
    )
    
    print(f"对话轮数: {len(result1.chat_history)}")
    print_message_flow(result1.chat_history[-2:], "最后两条消息")
    
    # 2. 轮数限制终止
    print("\n🔢 演示2: 轮数限制终止")
    
    assistant2 = AssistantAgent(
        name="assistant_rounds",
        system_message="你是一个助手，请简短回答问题。",
        llm_config=llm_config,
    )
    
    user_proxy2 = UserProxyAgent(
        name="user_rounds",
        human_input_mode="NEVER",
        max_consecutive_auto_reply=2,  # 最多2轮自动回复
        is_termination_msg=lambda x: False,  # 不使用关键词终止
        code_execution_config=False,
    )
    
    result2 = user_proxy2.initiate_chat(
        assistant2,
        message="请连续告诉我3个有趣的事实。"
    )
    
    print(f"对话轮数: {len(result2.chat_history)}")
    print_message_flow(result2.chat_history, "完整对话")


def demo_custom_termination():
    """
    演示自定义终止逻辑
    """
    print_section_header(
        "自定义终止逻辑", 
        "学习如何实现复杂的对话控制"
    )
    
    llm_config = get_openai_config()
    
    # 自定义终止函数：当消息包含特定内容或达到特定条件时终止
    def custom_termination_check(message):
        """
        自定义终止检查函数
        
        Args:
            message: 消息字典
            
        Returns:
            bool: 是否应该终止对话
        """
        content = message.get("content", "").lower()
        
        # 多种终止条件
        termination_keywords = ["再见", "结束", "完成", "terminate"]
        has_keyword = any(keyword in content for keyword in termination_keywords)
        
        # 消息长度检查
        is_too_long = len(content) > 500
        
        # 包含特定格式（比如总结）
        has_summary = "总结" in content and ":" in content
        
        should_terminate = has_keyword or is_too_long or has_summary
        
        if should_terminate:
            print(f"🛑 触发终止条件: 关键词={has_keyword}, 过长={is_too_long}, 总结={has_summary}")
        
        return should_terminate
    
    assistant = AssistantAgent(
        name="custom_assistant",
        system_message="""你是一个智能助手。
        请回答用户问题，当你觉得任务完成时，请说'总结：[你的总结内容]'。""",
        llm_config=llm_config,
    )
    
    user_proxy = UserProxyAgent(
        name="custom_user",
        human_input_mode="NEVER",
        max_consecutive_auto_reply=10,
        is_termination_msg=custom_termination_check,  # 使用自定义终止函数
        code_execution_config=False,
    )
    
    result = user_proxy.initiate_chat(
        assistant,
        message="请解释什么是机器学习，并在最后提供一个总结。"
    )
    
    print(f"对话轮数: {len(result.chat_history)}")
    print_message_flow(result.chat_history, "自定义终止对话")


def demo_human_input_modes():
    """
    演示不同的人机交互模式
    """
    print_section_header(
        "人机交互模式", 
        "理解AutoGen的人机协作机制"
    )
    
    print_learning_tip(
        "AutoGen的人机交互模式：\n"
        "- NEVER: 完全自动化\n"
        "- ALWAYS: 每次都需要人类输入\n"
        "- TERMINATE: 只在终止时需要人类确认"
    )
    
    llm_config = get_openai_config()
    
    # 模拟不同模式的配置（实际运行时为了演示方便，都设为NEVER）
    modes_demo = {
        "NEVER": "完全自动化，无需人类干预",
        "ALWAYS": "每轮都需要人类输入（演示中模拟）",
        "TERMINATE": "只在终止时需要人类确认（演示中模拟）"
    }
    
    for mode, description in modes_demo.items():
        print(f"\n🤝 模式: {mode}")
        print(f"描述: {description}")
        
        if mode == "NEVER":
            # 实际演示NEVER模式
            assistant = AssistantAgent(
                name=f"assistant_{mode.lower()}",
                system_message="你是一个助手，请简短回答并说'完成'结束。",
                llm_config=llm_config,
            )
            
            user_proxy = UserProxyAgent(
                name=f"user_{mode.lower()}",
                human_input_mode="NEVER",  # 完全自动
                max_consecutive_auto_reply=2,
                is_termination_msg=lambda x: "完成" in x.get("content", ""),
                code_execution_config=False,
            )
            
            result = user_proxy.initiate_chat(
                assistant,
                message="什么是AutoGen？"
            )
            
            print(f"  对话轮数: {len(result.chat_history)}")
        else:
            print(f"  （为了演示方便，此模式仅显示配置说明）")


def compare_with_langgraph_state():
    """
    对比AutoGen的消息传递与LangGraph的状态管理
    """
    print_section_header(
        "AutoGen vs LangGraph 状态管理对比", 
        "理解不同的状态管理方式"
    )
    
    # AutoGen方式
    autogen_approach = """
# AutoGen: 基于消息历史的隐式状态管理
def autogen_conversation():
    assistant = AssistantAgent("assistant")
    user_proxy = UserProxyAgent("user")
    
    # 对话历史自动维护
    result = user_proxy.initiate_chat(assistant, message="开始")
    
    # 状态通过消息历史传递
    conversation_state = result.chat_history
    return conversation_state
"""
    
    # LangGraph方式
    langgraph_approach = """
# LangGraph: 显式状态对象管理
from langgraph.graph import StateGraph
from typing import TypedDict

class ConversationState(TypedDict):
    messages: list
    current_step: str
    user_input: str

def langgraph_conversation():
    graph = StateGraph(ConversationState)
    
    # 显式定义状态转换
    graph.add_node("process", process_node)
    graph.add_edge("process", "respond")
    
    # 状态在节点间显式传递
    result = graph.invoke({"messages": [], "current_step": "start"})
    return result
"""
    
    print("🔄 状态管理对比:")
    print("\n[AutoGen - 隐式消息历史]")
    print(autogen_approach)
    
    print("\n[LangGraph - 显式状态对象]")
    print(langgraph_approach)
    
    print_learning_tip(
        "AutoGen的消息历史管理更自然，适合对话场景；"
        "LangGraph的显式状态管理更精确，适合复杂工作流。"
    )


@measure_execution_time
def main():
    """主函数：运行所有演示"""
    try:
        print_config_info()
        
        # 1. 终止条件演示
        demo_termination_conditions()
        
        print("\n" + "="*60 + "\n")
        
        # 2. 自定义终止逻辑
        demo_custom_termination()
        
        print("\n" + "="*60 + "\n")
        
        # 3. 人机交互模式
        demo_human_input_modes()
        
        print("\n" + "="*60 + "\n")
        
        # 4. 与LangGraph对比
        compare_with_langgraph_state()
        
        print_section_header(
            "Demo 2 完成！", 
            "您已经掌握了AutoGen的对话控制机制。接下来可以运行Demo 3进行框架对比。"
        )
        
    except Exception as e:
        print(f"❌ 运行出错: {e}")
        print("\n请检查:")
        print("1. .env文件中的API配置是否正确")
        print("2. 网络连接是否正常")
        print("3. 依赖包是否正确安装")


if __name__ == "__main__":
    main()
