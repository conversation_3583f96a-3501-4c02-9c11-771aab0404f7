# AutoGen学习演示项目

针对有LangChain/LangGraph经验的开发者设计的AutoGen快速上手项目。

## 项目结构

```
autogen-learning-demos/
├── pyproject.toml          # 项目配置和依赖
├── .env                    # 环境变量配置
├── README.md              # 项目说明
├── config/                # 配置文件
│   └── llm_config.py      # LLM配置管理
├── demos/                 # 演示项目
│   ├── demo1_basic_chat.py        # Demo 1: 基础对话
│   ├── demo2_conversation_control.py  # Demo 2: 对话控制
│   ├── demo3_framework_comparison.py  # Demo 3: 框架对比
│   └── utils/             # 工具函数
│       ├── __init__.py
│       └── helpers.py
├── langchain_examples/    # LangChain对比示例
│   ├── __init__.py
│   └── basic_chain.py
└── work_dir/             # AutoGen工作目录

```

## 环境设置

### 1. 使用uv安装依赖

```bash
# 安装uv（如果还没有安装）
curl -LsSf https://astral.sh/uv/install.sh | sh

# 创建虚拟环境并安装依赖
uv venv
source .venv/bin/activate  # Linux/Mac
# 或者 .venv\Scripts\activate  # Windows

# 安装项目依赖
uv pip install -e .
```

### 2. 配置环境变量

确保 `.env` 文件中的API密钥正确配置。

### 3. 创建工作目录

```bash
mkdir -p work_dir
```

## 演示项目说明

### Demo 1: AutoGen基础对话
- **文件**: `demos/demo1_basic_chat.py`
- **目标**: 理解AutoGen的基本对话机制
- **重点**: AssistantAgent和UserProxyAgent的创建和交互

### Demo 2: 对话控制和终止
- **文件**: `demos/demo2_conversation_control.py`
- **目标**: 掌握对话流程控制和终止条件
- **重点**: 人机交互模式和自定义终止逻辑

### Demo 3: 框架对比实验
- **文件**: `demos/demo3_framework_comparison.py`
- **目标**: 对比AutoGen和LangChain的实现差异
- **重点**: 理解不同框架的编程范式差异

## 运行演示

```bash
# 运行Demo 1
python demos/demo1_basic_chat.py

# 运行Demo 2
python demos/demo2_conversation_control.py

# 运行Demo 3
python demos/demo3_framework_comparison.py
```

## 学习重点

1. **概念映射**: 理解AutoGen概念与LangChain/LangGraph的对应关系
2. **编程范式**: 对话式 vs 链式/图式编程
3. **状态管理**: 消息历史 vs 显式状态对象
4. **控制流**: 自然对话流转 vs 预定义路径

## 预期学习成果

完成这些演示后，您应该能够：
- 理解AutoGen的核心概念和编程模式
- 识别AutoGen与LangChain/LangGraph的关键差异
- 选择合适的框架解决特定问题
- 快速上手AutoGen开发
