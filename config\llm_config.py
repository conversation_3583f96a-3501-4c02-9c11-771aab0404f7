"""
LLM配置管理模块
统一管理AutoGen和LangChain的LLM配置
"""

import os
from typing import Dict, Any
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()


def get_openai_config() -> Dict[str, Any]:
    """
    获取OpenAI配置，适用于AutoGen
    
    Returns:
        Dict[str, Any]: AutoGen格式的LLM配置
    """
    return {
        "config_list": [
            {
                "model": os.getenv("OPENAI_MODEL", "gpt-4o-mini"),
                "api_key": os.getenv("OPENAI_API_KEY"),
                "base_url": os.getenv("OPENAI_BASE_URL"),
            }
        ],
        "temperature": 0.7,
        "timeout": 120,
    }


def get_langchain_config() -> Dict[str, Any]:
    """
    获取LangChain配置
    
    Returns:
        Dict[str, Any]: LangChain格式的配置
    """
    return {
        "model": os.getenv("OPENAI_MODEL", "gpt-4o-mini"),
        "openai_api_key": os.getenv("OPENAI_API_KEY"),
        "openai_api_base": os.getenv("OPENAI_BASE_URL"),
        "temperature": 0.7,
        "max_tokens": 1000,
    }


def get_work_dir() -> str:
    """
    获取AutoGen工作目录
    
    Returns:
        str: 工作目录路径
    """
    work_dir = os.getenv("AUTOGEN_WORK_DIR", "./work_dir")
    os.makedirs(work_dir, exist_ok=True)
    return work_dir


def print_config_info():
    """打印配置信息（隐藏敏感信息）"""
    print("=== 配置信息 ===")
    print(f"模型: {os.getenv('OPENAI_MODEL', 'gpt-4o-mini')}")
    print(f"API Base URL: {os.getenv('OPENAI_BASE_URL', 'https://api.openai.com/v1')}")
    print(f"工作目录: {get_work_dir()}")
    api_key = os.getenv("OPENAI_API_KEY", "")
    if api_key:
        print(f"API Key: {api_key[:10]}...{api_key[-4:] if len(api_key) > 14 else '***'}")
    else:
        print("⚠️  警告: 未设置OPENAI_API_KEY")
    print("=" * 20)


if __name__ == "__main__":
    print_config_info()
